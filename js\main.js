
// Header scroll effect
let header = document.querySelector("header")
window.onscroll = function(){
    if(this.scrollY >= 50){
        header.classList.add("active")
    }else{
        header.classList.remove("active")
    }
}

// Smooth scrolling for navigation links
document.querySelectorAll('header nav .links a').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        if(targetId.startsWith('#')) {
            const targetSection = document.querySelector(targetId);
            if(targetSection) {
                const headerHeight = header.offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        }
    });
});