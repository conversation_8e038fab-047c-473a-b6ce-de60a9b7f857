@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&family=Inter:wght@400;500&display=swap");

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    text-decoration: none;
    list-style-type: none;
    font-family: "Inter", sans-serif;

}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

html,
body {
    max-width: 100%;
    overflow-x: hidden;
}

:root {
    --main_color: #c9f31d;
    --p_color: #BaBaBa;
    --bg_color: #fff;
    --black_color: #070707;
    --border_color: rgba(255, 255, 0.1);

}

body {
    background: #131313;
    overflow-x: hidden;
}

.container {
    width: 80%;
    margin: auto;
    max-width: 1500px;
    padding: 0 15px;
    box-sizing: border-box;
}

section {
    padding: 80px 0;
}

img {
    width: 100%;
    height: auto;
    max-width: 100%;
    display: block;
}

h1 h2 h3 h4 h5 h6 {
    color: var(--white_color);
    font-family: "DM sans", sans-serif;
}

span {
    color: var(--main_color);
}



p {
    color: var(--p_color);

}

/* btns*/
.btns {
    display: flex;

    align-items: center;
    gap: 20px;

}

.btn {
    background: var(--main_color);
    padding: 14px 10px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: capitalize;
    color: var(--black_color);
    transition: 0.3s;

}

.btn:hover {
    scale: 1.1;

}


/* bg-lines*/
.bg-lines {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    width: 100%;
    overflow: hidden;
}

.bg-lines span {
    position: absolute;
    top: 0;
    z-index: -1;
    height: 100%;
    background: rgb(255, 255, 255, 0.06);
    width: 2px;
    -webkit-animation: left_Right 18s infinite;
    animation: left_Right 18s infinite;
}

.bg-lines span:nth-child(1) {
    left: 10%;
}

.bg-lines span:nth-child(2) {
    left: 20%;
}

.bg-lines span:nth-child(3) {
    left: 30%;
}

.bg-lines span:nth-child(4) {
    left: 40%;
}

.bg-lines span:nth-child(5) {
    left: 50%;
}

.bg-lines span:nth-child(6) {
    left: 60%;
}

.bg-lines span:nth-child(7) {
    left: 70%;
}

.bg-lines span:nth-child(8) {
    left: 80%;
}

.bg-lines span:nth-child(9) {
    left: 90%;
}

@keyframes left_Right {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0);

    }

    50% {
        -webkit-transform: translateX(-100px);
        transform: translateX(-100px);
    }

    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}

.top_section {
    text-align: center;
    margin-bottom: 100px;

}

.top_section h2 {
    font-size: 30px;
    color: var(--bg_color);
}

/* start header*/

header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    transition: 0.3s;
    z-index: 1000;
    background: rgba(7, 7, 7, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

header.overflow,
header.active {
    background: rgba(7, 7, 7, 0.98);
    border-bottom: 1px solid #565655;
    backdrop-filter: blur(15px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

header nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

header nav.logo img {
    width: 200px;
    max-width: 100%;

}

header nav .links {
    display: flex;
    gap: 40px;
}

header nav .links a {
    color: white;
    text-transform: capitalize;
    font-size: 18px;
    transition: 0.3s;

}

header nav .links a:hover {
    color: #c9f31d;
}

header nav .icons {
    display: flex;
    gap: 30px;

}

header nav .icons a {
    color: var(--main_color);
    font-size: 28px;
    transition: 0.3s;

}

header nav .icons a:hover {
    scale: 1.4;
}

/* hero*/
.hero {
    padding-top: 120px;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 70px);
}

.hero .container {
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.hero .div_text {
    width: 50%;

}

.hero .div_text h4 {
    color: var(--p_color);
    font-size: 36px;
    margin-bottom: 15px;

}

.hero .div_text h1 {
    font-size: 70px;

}

.hero .div_text h2 {
    font-size: 70px;
    font-weight: normal;
    line-height: 0.9;
    color: var(--p_color);
}

.hero .div_img {
    width: 40%;
    position: relative;
    border-radius: 0 0 600px 600px;
    overflow: hidden;
}

.hero .div_text p {
    margin: 30px 0;
    font-size: 18px;
    width: 80%;
    line-height: 1.7;

}

.hero .btn {
    font-size: 20px;
    padding: 18px 50px;

}

.hero .div_img .bg_img {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -1;

}

.btn_no_bg {
    color: var(--bg_color);
    background: var(--main_color);
    padding: 14px 10px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: capitalize;
    color: var(--black_color);
    transition: 0.3s;
}

.btn_no_bg:hover {
    scale: 1.1;
}

/* start about*/
.about .container {
    background: var(--black_color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 5px;
    padding: 50px;


}

.about .div_text {
    width: 50%;


}

.about .div_text h5 {
    color: var(--p_color);
    font-size: 18px;
    text-transform: capitalize;


}

.about .div_text h2 {
    font-size: 45px;
    margin-bottom: 20px;


}

.about .div_text p {
    line-height: 1.8;

}

.about .div_text ul {
    display: flex;
    color: var(--bg_color);
    margin: 30 0 20px;
    flex-wrap: wrap;

}

.about .div_text ul li {
    width: 45%;
    font-weight: bold;
    display: flex;
    gap: 10px;
    margin-bottom: 30;


}

.about .div_text ul li i {
    color: var(--main_color);

}

.about .div_img {
    width: 35%;


}

.about .div_text {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

}

.about .spacer {
    flex-grow: 1;
}

.about .div_text .btn {
    width: 200px;
    margin: 20px auto 0 auto;
    display: block;
    text-align: center;
}

/* end about section */
/* start services*/
.services .boxs {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

}

.services .boxs .box {
    width: 30%;
    text-align: center;
    padding: 50px 20px;
    border: 1px solid var(--p_color);
    transition: 0.3s;

}

.services .boxs .box i {
    color: var(--main_color);
    font-size: 70px;
    margin-bottom: 30px;

}

.services .boxs .box h3 {
    font-size: 25px;
    color: var(--bg_color);
    margin-bottom: 20px;

}

.services .boxs .box p {
    margin-bottom: 40px;
    font-size: 14px;
    line-height: 1.7;

}

.services .boxs .box:hover {
    border-color: var(--main_color);
    scale: 1.1;
}

/* End services*/

/* start Projects*/


.projects .project_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50px;


}

.projects .project_box img {
    width: 45%;

}

.projects .project_box .text {
    margin: 50px;
    width: calc(50% - 50px);
    max-width: 100%;
    box-sizing: border-box;
}

.projects .project_box .text h4 {
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: normal;

}

.projects .project_box .text h3 a {
    font-size: 40px;
    color: var(--bg_color);

    transition: 0.3s;

}

.projects .project_box .text h3 a:hover {
    color: var(--main_color);
    scale: 1.1;

}

.projects .project_box .text p {

    margin: 30px 0 50px;
    line-height: 1.2;

}

.projects .project_box .link {
    display: block;
    width: 50px;
    height: 50px;
    border: 0.5px solid var(--border_color);
    text-align: center;
    line-height: 50px;
    color: var(--bg_color);
    font-size: 20px;
    transition: 0.3s;
    border-radius: 50px;
    background-color: var(--main_color);
    rotate: 45deg;


}

.projects .project_box .link:hover {
    scale: 1.2;

}

.projects .project_box.project_box_2 {
    flex-direction: row-reverse;
}

.projects .project_box.project_box_4 {
    flex-direction: row-reverse;
}

/* End projects*/

/* start Contact*/
.contact .container {
    display: flex;
    justify-content: space-between;


}

.contact .top_section {
    margin: 50px 0;
    padding: 0 20px;

}

.contact .soil_contact {
    width: 30%;

}

.contact .soil_contact h2 {
    color: var(--bg_color);
    margin-bottom: 40px;

}

.contact .soil_contact .links {
    display: flex;
    flex-direction: column;

}

.contact .soil_contact .links a {
    color: var(--bg_color);
    font-size: 20px;
    margin-bottom: 25px;
    position: relative;
    left: 0;
    transition: 0.3s;
    display: inline-flex;
    align-items: center;

}

.contact .soil_contact .links a i {
    color: var(--black_color);
    margin-right: 10px;
    width: 45px;
    height: 45px;
    background-color: var(--main_color);

}

.contact .soil_contact .links a i {
    display: block;
    align-items: center;
    text-align: center;
    line-height: 45px;
    border: 15px;

}

.contact .soil_contact .links a:hover {
    padding-left: 15px;

}

.contact form {
    width: 60%;
    text-align: center;

}

.contact form .input_form {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

}

.contact form .inputs_form input,
.contact form .inputs_form textarea {
    border-radius: 5px;
    padding: 20px;
    outline: none;
    background-color: var(--black_color);
    color: white;
    margin-bottom: 30px;
    border: 1px solid var(--p_color);
    transition: 0.3s;

}

.contact form .inputs_form input {
    width: 48%;

}

.contact form .inputs_form textarea {
    width: 100%;

}

.contact form .inputs_form input:focus,
.contact form .inputs_form textarea:focus {
    border: 2px solid var(--main_color);


}

.contact .btn {
    outline: none;
    padding: 15px 35px;
    font-size: 20px;
    cursor: pointer;
    transition: 0.3;

}




/* End Contact*/

/* ========== RESPONSIVE DESIGN ========== */

/* General Responsive Utilities */
* {
    box-sizing: border-box;
}

/* Prevent horizontal scroll */
.overflow-hidden {
    overflow-x: hidden;
}

/* Ensure no element exceeds viewport width */
*,
*::before,
*::after {
    max-width: 100%;
    box-sizing: border-box;
}

/* Ensure all flex containers are responsive */
.hero .container,
.about .container,
.services .boxs,
.projects .container,
.contact .container {
    flex-wrap: wrap;
    max-width: 100%;
}

/* Prevent any element from causing horizontal scroll */
.container {
    max-width: 100%;
    box-sizing: border-box;
}

/* Ensure images don't overflow */
img {
    max-width: 100%;
    height: auto;
    box-sizing: border-box;
}

/* Prevent text overflow */
h1,
h2,
h3,
h4,
h5,
h6,
p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

/* Large Tablets and Small Laptops (1024px and below) */
@media (max-width: 1024px) {
    .container {
        width: 90%;
    }

    .hero .div_text h1,
    .hero .div_text h2 {
        font-size: 50px;
    }

    .hero .div_text h4 {
        font-size: 28px;
    }

    .about .div_text h2 {
        font-size: 35px;
    }

    .projects .project_box .text h3 a {
        font-size: 30px;
    }

    .projects .project_box .text {
        margin: 50px;
        width: calc(50% - 50px);
    }
}

/* Tablets (768px and below) */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 0 10px;
    }

    /* Header Navigation */
    header {
        height: auto;
        position: fixed;
        background: rgba(7, 7, 7, 0.98);
        backdrop-filter: blur(15px);
    }

    header nav {
        flex-direction: column;
        height: auto;
        padding: 15px 0;
    }

    header nav .links {
        gap: 20px;
        margin: 15px 0;
        flex-wrap: wrap;
        justify-content: center;
    }

    header nav .links a {
        font-size: 16px;
        padding: 8px 12px;
        border-radius: 5px;
        transition: 0.3s;
    }

    header nav .links a:hover,
    header nav .links a.active {
        background-color: var(--main_color);
        color: var(--black_color);
    }

    header nav .icons {
        gap: 20px;
    }

    /* Hero Section */
    .hero {
        height: auto;
        padding: 150px 0 50px 0;
        margin-top: 0;
    }

    .hero .container {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }

    .hero .div_text,
    .hero .div_img {
        width: 100%;
    }

    .hero .div_text h1,
    .hero .div_text h2 {
        font-size: 40px;
    }

    .hero .div_text h4 {
        font-size: 24px;
    }

    .hero .div_text p {
        width: 100%;
        font-size: 16px;
    }

    .hero .btns {
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
        width: 100%;
    }

    .hero .btn,
    .btn_no_bg {
        width: 200px;
        max-width: 90%;
        text-align: center;
        display: inline-block;
        box-sizing: border-box;
    }

    /* About Section */
    .about .container {
        flex-direction: column;
        text-align: center;
        gap: 40px;
        padding: 30px;
    }

    .about .div_text,
    .about .div_img {
        width: 100%;
    }

    .about .div_text h2 {
        font-size: 30px;
    }

    /* Services Section */
    .services .boxs {
        flex-direction: column;
        gap: 30px;
    }

    .services .boxs .box {
        width: 100%;
    }

    /* Projects Section */
    .projects .project_box,
    .projects .project_box.project_box_2,
    .projects .project_box.project_box_4 {
        flex-direction: column;
        text-align: center;
    }

    .projects .project_box img {
        width: 100%;
    }

    .projects .project_box .text {
        width: 100%;
        margin: 30px 0;
    }

    .projects .project_box .text h3 a {
        font-size: 25px;
    }

    /* Contact Section */
    .contact .container {
        flex-direction: column;
        gap: 40px;
    }

    .contact .soil_contact,
    .contact form {
        width: 100%;
    }

    .contact .top_section {
        margin: 50px 0;
    }

    .contact form .inputs_form input {
        width: 100%;
    }
}

/* Mobile Phones (480px and below) */
@media (max-width: 480px) {

    /* General */
    section {
        padding: 50px 0;
    }

    .container {
        width: 95%;
        padding: 0 10px;
    }

    /* Background Lines */
    .bg-lines span {
        display: none;
    }

    /* Header */
    header {
        position: fixed;
        background: rgba(7, 7, 7, 0.98);
        backdrop-filter: blur(15px);
    }

    header nav .links {
        gap: 15px;
    }

    header nav .links a {
        font-size: 14px;
    }

    header nav .icons a {
        font-size: 24px;
    }

    /* Hero Section */
    .hero {
        padding-top: 180px;
    }

    .hero .div_text h1,
    .hero .div_text h2 {
        font-size: 28px;
        line-height: 1.2;
    }

    .hero .div_text h4 {
        font-size: 18px;
    }

    .hero .div_text p {
        font-size: 14px;
        line-height: 1.6;
    }

    .hero .btn {
        font-size: 16px;
        padding: 12px 30px;
    }

    .btn_no_bg {
        font-size: 16px;
        padding: 12px 30px;
    }

    /* About Section */
    .about .container {
        padding: 20px;
    }

    .about .div_text h2 {
        font-size: 24px;
    }

    .about .div_text h4 {
        font-size: 16px;
    }

    .about .div_text p {
        font-size: 14px;
        line-height: 1.6;
    }

    /* Services Section */
    .services .boxs .box {
        padding: 30px 20px;
    }

    .services .boxs .box h3 {
        font-size: 20px;
    }

    .services .boxs .box p {
        font-size: 14px;
        line-height: 1.6;
    }

    /* Projects Section */
    .projects .project_box .text h3 a {
        font-size: 20px;
    }

    .projects .project_box .text h4 {
        font-size: 16px;
    }

    .projects .project_box .text p {
        font-size: 14px;
        line-height: 1.6;
        margin: 20px 0 30px;
    }

    /* Contact Section */
    .contact .soil_contact .links a {
        font-size: 16px;
        margin-bottom: 20px;
    }

    .contact .soil_contact .links a i {
        width: 35px;
        height: 35px;
        line-height: 35px;
        margin-right: 8px;
    }

    .contact form .inputs_form input,
    .contact form .inputs_form textarea {
        padding: 15px;
        font-size: 14px;
    }

    .contact .btn {
        padding: 12px 25px;
        font-size: 16px;
    }

    /* Top Sections */
    .top_section {
        margin-bottom: 50px;
    }

    .top_section h2 {
        font-size: 24px;
    }
}

/* Extra Small Devices (320px and below) */
@media (max-width: 320px) {
    .container {
        width: 98%;
        padding: 0 5px;
    }

    .hero .div_text h1,
    .hero .div_text h2 {
        font-size: 24px;
    }

    .hero .div_text h4 {
        font-size: 16px;
    }

    .about .div_text h2 {
        font-size: 20px;
    }

    .services .boxs .box {
        padding: 20px 15px;
    }

    .projects .project_box .text h3 a {
        font-size: 18px;
    }

    .top_section h2 {
        font-size: 20px;
    }

    .contact .soil_contact h2 {
        font-size: 20px;
    }
}